#!/usr/bin/env python3
"""
Neo4j AuraDB management helper for Terraform.

This script is invoked in two modes:

- action=plan  (via Terraform data external):
  - Inputs (stdin JSON):
      {
        "api_base_url": "https://api.neo4j.io/v1",
        "access_token": "...",
        "tenant_id": "...",
        "neo4j_customers": [{"database_name": "...", "tenant_name": "...", "facility_name": "..."}, ...]
      }
  - Behavior: GET /instances?tenantId=TENANT_ID, compute which database_names are missing.
  - Output: { "to_create_json": "[ ... ]", "to_create_hash": "..." }

- action=apply (via Terraform null_resource local-exec):
  - Inputs (stdin JSON):
      {
        "api_base_url": "https://api.neo4j.io/v1",
        "access_token": "...",
        "tenant_id": "...",
        "neo4j_version": "5",
        "aura_region": "us-east1",
        "instance_memory": "1GB",
        "instance_storage": "2GB",
        "aura_type": "professional-db",
        "to_create": ["db1", "db2"],
        "results_path": "/absolute/path/.neo4j_created.json"
      }
  - Behavior: For each in to_create, POST /instances and collect credentials.
  - Side effect: writes results_path JSON with a map keyed by database_name containing instance_id, username, password.
  - Output: { "status": "ok", "created_count": "N" }

- action=read_created (via Terraform data external):
  - Inputs (stdin JSON): { "results_path": "/absolute/path/.neo4j_created.json" }
  - Output: { "created_map_json": "{ ... }" }  # or "{}" if file absent/empty
"""

from __future__ import annotations

import hashlib
import io
import json
import os
import sys
import time
import urllib.parse
import urllib.request
import urllib.error
import ssl
from typing import Any, Dict, List, Tuple


def read_stdin_json() -> Dict[str, Any]:
    data = sys.stdin.read()
    try:
        return json.loads(data) if data else {}
    except json.JSONDecodeError as exc:
        # Avoid recursion into fatal before we have a working stdout
        sys.stdout.write(json.dumps({"error": f"invalid JSON on stdin: {exc}"}))
        sys.exit(1)


def write_stdout_json(obj: Dict[str, Any]) -> None:
    # Ensure all values are strings for Terraform external data compatibility
    normalized: Dict[str, str] = {}
    for k, v in obj.items():
        if isinstance(v, str):
            normalized[k] = v
        else:
            normalized[k] = json.dumps(v, separators=(",", ":"))
    sys.stdout.write(json.dumps(normalized))
    sys.stdout.flush()


def fatal(message: str, code: int = 1) -> None:
    try:
        sys.stderr.write(f"neo4j_manage.py error: {message}\n")
        sys.stderr.flush()
    except (OSError, IOError):
        # Handle stderr write failures
        pass
    write_stdout_json({"error": message})
    sys.exit(code)


def http_request(
    method: str,
    url: str,
    headers: Dict[str, str] | None = None,
    body: bytes | None = None,
    timeout: int = 30,
    verify: bool = True,
    cafile: str | None = None,
    exc: Exception | None = None,
) -> Tuple[int, Dict[str, Any]]:
    req = urllib.request.Request(url=url, method=method)
    if headers:
        for k, v in headers.items():
            req.add_header(k, v)
    if body is not None:
        req.data = body
    # SSL context
    context: ssl.SSLContext | None
    if verify:
        try:
            context = ssl.create_default_context(cafile=cafile)
        except (ssl.SSLError, FileNotFoundError, OSError):
            context = ssl.create_default_context()
    else:
        context = ssl._create_unverified_context()  # noqa: S303
    try:
        with urllib.request.urlopen(req, timeout=timeout, context=context) as resp:
            status = resp.getcode()
            content_type = resp.headers.get("Content-Type", "application/json")
            raw = resp.read()
    except urllib.error.HTTPError as e:
        status = e.getcode()
        raw = e.read() if hasattr(e, "read") else b""
    except (urllib.error.URLError, ConnectionError, TimeoutError, OSError) as e:
        # Handle network errors by setting appropriate status and error message
        status = 0  # Use 0 to indicate network/connection failure
        error_msg = f"Network error: {type(e).__name__}: {str(e)}"
        raw = json.dumps({"error": error_msg}).encode("utf-8")

    try:
        payload = json.loads(raw.decode("utf-8") or "{}")
    except (json.JSONDecodeError, UnicodeDecodeError):
        payload = {"raw": raw.decode("utf-8", errors="replace")}
    return status, payload


def sha256_hex(text: str) -> str:
    return hashlib.sha256(text.encode("utf-8")).hexdigest()


def do_plan(inputs: Dict[str, Any]) -> None:
    api_base_url: str | None = inputs.get("api_base_url")
    access_token: str | None = inputs.get("access_token")
    tenant_id: str | None = inputs.get("tenant_id")

    missing = [
        k
        for k, v in {
            "api_base_url": api_base_url,
            "access_token": access_token,
            "tenant_id": tenant_id,
        }.items()
        if not v
    ]
    if missing:
        # Non-fatal for plan: return empty create set but include error
        write_stdout_json(
            {
                "to_create_json": "[]",
                "to_create_hash": "",
                "error": f"missing required inputs for plan: {', '.join(missing)}",
            }
        )
        return
    customers_input = inputs.get("neo4j_customers", [])
    if isinstance(customers_input, str):
        try:
            customers: List[Dict[str, str]] = (
                json.loads(customers_input) if customers_input else []
            )
        except (json.JSONDecodeError, TypeError):
            customers = []
    else:
        customers = customers_input

    # Fetch existing instances filtered by tenantId
    query = urllib.parse.urlencode({"tenantId": tenant_id})
    url = f"{api_base_url}/instances?{query}"
    cafile_env = os.environ.get("SSL_CERT_FILE") or os.environ.get("REQUESTS_CA_BUNDLE")
    try:
        status, payload = http_request(
            method="GET",
            url=url,
            headers={
                "Authorization": f"Bearer {access_token}",
                "Accept": "application/json",
            },
            verify=True,
            cafile=cafile_env,
        )
    except (urllib.error.URLError, ConnectionError, TimeoutError, OSError) as exc:
        # Retry insecure for plan only, to avoid blocking planning in corp networks
        try:
            status, payload = http_request(
                method="GET",
                url=url,
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Accept": "application/json",
                },
                verify=False,
                cafile=None,
                exc=exc,
            )
        except (urllib.error.URLError, ConnectionError, TimeoutError, OSError) as exc2:
            write_stdout_json(
                {
                    "to_create_json": "[]",
                    "to_create_hash": "",
                    "error": f"GET /instances exception (verify on and off failed): {exc2}",
                }
            )
            return

    if status != 200:
        write_stdout_json(
            {
                "to_create_json": "[]",
                "to_create_hash": "",
                "error": f"GET /instances failed with status {status}: {json.dumps(payload)[:500]}",
            }
        )
        return

    data = payload.get("data", [])
    existing_names = {str(item.get("name", "")) for item in data}
    to_create = [
        c["database_name"]
        for c in customers
        if c.get("database_name") not in existing_names
    ]

    sorted_list = sorted(to_create)
    out_json = json.dumps(sorted_list, separators=(",", ":"))
    out_hash = sha256_hex("|".join(sorted_list))

    write_stdout_json(
        {
            "to_create_json": out_json,
            "to_create_hash": out_hash,
        }
    )


def do_apply(inputs: Dict[str, Any]) -> None:
    api_base_url: str | None = inputs.get("api_base_url")
    access_token: str | None = inputs.get("access_token")
    tenant_id: str | None = inputs.get("tenant_id")
    version: str | None = inputs.get("neo4j_version")
    region: str | None = inputs.get("aura_region")
    memory: str | None = inputs.get("instance_memory")
    storage: str | None = inputs.get("instance_storage")
    aura_type: str | None = inputs.get("aura_type")
    to_create: List[str] = inputs.get("to_create", [])
    results_path: str = inputs.get("results_path", "/tmp/neo4j_created.json")

    missing = [
        k
        for k, v in {
            "api_base_url": api_base_url,
            "access_token": access_token,
            "tenant_id": tenant_id,
            "neo4j_version": version,
            "aura_region": region,
            "instance_memory": memory,
            "instance_storage": storage,
            "aura_type": aura_type,
        }.items()
        if not v
    ]
    if missing:
        fatal(f"missing required inputs for apply: {', '.join(missing)}")

    created_map: Dict[str, Dict[str, str]] = {}

    for name in to_create:
        body = json.dumps(
            {
                "name": name,
                "version": version,
                "region": region,
                "memory": memory,
                "storage": storage,
                "cloud_provider": "gcp",
                "type": aura_type,
                "tenant_id": tenant_id,
            },
            separators=(",", ":"),
        ).encode("utf-8")

        cafile_env = os.environ.get("SSL_CERT_FILE") or os.environ.get(
            "REQUESTS_CA_BUNDLE"
        )
        try:
            status, payload = http_request(
                method="POST",
                url=f"{api_base_url}/instances",
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                    "Accept": "application/json",
                },
                body=body,
                timeout=60,
                verify=True,
                cafile=cafile_env,
            )
        except (urllib.error.URLError, ConnectionError, TimeoutError, OSError) as exc:
            fatal(
                f"POST /instances connection failed for {name}: {exc}. "
                f"If this is due to corporate TLS inspection, set SSL_CERT_FILE "
                f"or REQUESTS_CA_BUNDLE to your CA bundle, "
                f"or (not recommended) run with insecure TLS."
            )

        if status not in (200, 201):
            fatal(
                f"POST /instances for {name} failed with status {status}: {json.dumps(payload)[:500]}"
            )

        data = payload.get("data", {})
        instance_id = str(data.get("id", ""))
        username = str(data.get("username", ""))
        password = str(data.get("password", ""))
        if not (instance_id and username and password):
            fatal(f"Invalid response creating {name}: missing required fields")

        response_hash = sha256_hex(json.dumps(payload, separators=(",", ":")))
        created_map[name] = {
            "instance_id": instance_id,
            "username": username,
            "password": password,
            "database_name": name,
            "response_hash": response_hash,
        }

    # Persist results for subsequent data source read
    try:
        with open(results_path, "w", encoding="utf-8") as f:
            json.dump(created_map, f, separators=(",", ":"))
    except Exception as e:  # noqa: BLE001
        fatal(f"failed to write results file {results_path}: {e}")

    write_stdout_json({"status": "ok", "created_count": str(len(created_map))})


def do_read_created(inputs: Dict[str, Any]) -> None:
    results_path: str = inputs.get("results_path", "/tmp/neo4j_created.json")
    if not os.path.exists(results_path):
        write_stdout_json({"created_map_json": "{}"})
        return
    try:
        with open(results_path, "r", encoding="utf-8") as f:
            content = f.read() or "{}"
    except Exception as e:  # noqa: BLE001
        fatal(f"failed to read results file {results_path}: {e}")
    write_stdout_json({"created_map_json": content})

    # Secrets are created by Terraform based on created_map


def main() -> None:
    try:
        inputs = read_stdin_json()
        action = inputs.get("action", "plan")

        if action == "plan":
            return do_plan(inputs)
        if action == "apply":
            return do_apply(inputs)
        if action == "read_created":
            return do_read_created(inputs)
        fatal(f"unknown action: {action}")
    except Exception as exc:  # noqa: BLE001
        # Return a JSON error for Terraform to display
        write_stdout_json({"error": f"unhandled exception: {exc}"})
        sys.exit(1)


if __name__ == "__main__":
    main()
